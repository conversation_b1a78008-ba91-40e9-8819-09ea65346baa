import { supabase } from './supabase'
import type { IInvestmentTransaction, IInvestmentForm, IInvestmentHolding, IAccount } from '../types'
import type { TablesInsert } from '../database.types'

export class InvestmentService {
  /**
   * Create an investment transaction (buy/sell)
   * This also creates a transfer from a funding account for buy transactions
   */
  static async createInvestmentTransaction(
    investmentData: IInvestmentForm,
    fundingAccountId?: string // Required for buy transactions
  ): Promise<IInvestmentTransaction> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    // Validate investment account exists and is an investment account
    const { data: investmentAccount } = await supabase
      .from('accounts')
      .select('*')
      .eq('id', investmentData.account_id)
      .eq('user_id', user.id)
      .single()

    if (!investmentAccount || investmentAccount.account_type !== 'investment') {
      throw new Error('Invalid investment account specified')
    }

    // For buy transactions, validate funding account and check balance
    if (investmentData.transaction_type === 'investment_buy') {
      if (!fundingAccountId) {
        throw new Error('Funding account required for investment purchases')
      }

      const { data: fundingAccount } = await supabase
        .from('accounts')
        .select('*')
        .eq('id', fundingAccountId)
        .eq('user_id', user.id)
        .single()

      if (!fundingAccount) {
        throw new Error('Invalid funding account specified')
      }

      const totalCost = investmentData.amount + (investmentData.fees || 0)
      if (fundingAccount.current_balance < totalCost) {
        throw new Error('Insufficient balance in funding account')
      }
    }

    // Get investment category
    const categoryName = investmentData.transaction_type === 'investment_buy' 
      ? 'Investment Purchase' 
      : 'Investment Sale'
    
    const { data: category } = await supabase
      .from('categories')
      .select('id')
      .eq('name', categoryName)
      .eq('is_system', true)
      .single()

    if (!category) {
      throw new Error(`${categoryName} category not found`)
    }

    // Create the investment transaction
    const transactionData: TablesInsert<'transactions'> = {
      amount: investmentData.amount,
      description: investmentData.description || 
        `${investmentData.transaction_type === 'investment_buy' ? 'Buy' : 'Sell'} ${investmentData.investment_quantity} shares of ${investmentData.investment_symbol}`,
      category_id: category.id,
      account_id: investmentData.account_id,
      transaction_type: investmentData.transaction_type,
      transaction_date: investmentData.transaction_date,
      transaction_status: 'completed',
      fees: investmentData.fees || 0,
      investment_symbol: investmentData.investment_symbol,
      investment_quantity: investmentData.investment_quantity,
      investment_price: investmentData.investment_price,
      user_id: user.id,
    }

    const { data: transaction, error: transactionError } = await supabase
      .from('transactions')
      .insert(transactionData)
      .select(`
        *,
        category:categories(*),
        account:accounts(*)
      `)
      .single()

    if (transactionError) {
      throw new Error(`Failed to create investment transaction: ${transactionError.message}`)
    }

    // For buy transactions, create a transfer from funding account to investment account
    if (investmentData.transaction_type === 'investment_buy' && fundingAccountId) {
      const { TransferService } = await import('./transfers')
      
      try {
        await TransferService.createTransfer({
          amount: investmentData.amount + (investmentData.fees || 0),
          description: `Investment purchase: ${investmentData.investment_symbol}`,
          from_account_id: fundingAccountId,
          to_account_id: investmentData.account_id,
          transaction_date: investmentData.transaction_date,
          fees: 0, // Fees already accounted for in investment transaction
        })
      } catch (error) {
        // If transfer fails, rollback the investment transaction
        await supabase
          .from('transactions')
          .delete()
          .eq('id', transaction.id)
        
        throw new Error(`Failed to create funding transfer: ${error instanceof Error ? error.message : 'Unknown error'}`)
      }
    }

    return transaction as IInvestmentTransaction
  }

  /**
   * Get investment transactions for a user or specific account
   */
  static async getInvestmentTransactions(options?: {
    account_id?: string
    symbol?: string
    transaction_type?: 'investment_buy' | 'investment_sell'
    startDate?: string
    endDate?: string
    limit?: number
    offset?: number
  }): Promise<{ data: IInvestmentTransaction[]; count: number }> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    let query = supabase
      .from('transactions')
      .select(`
        *,
        category:categories(*),
        account:accounts(*)
      `, { count: 'exact' })
      .eq('user_id', user.id)
      .in('transaction_type', ['investment_buy', 'investment_sell'])
      .order('transaction_date', { ascending: false })
      .order('created_at', { ascending: false })

    if (options?.account_id) {
      query = query.eq('account_id', options.account_id)
    }

    if (options?.symbol) {
      query = query.eq('investment_symbol', options.symbol)
    }

    if (options?.transaction_type) {
      query = query.eq('transaction_type', options.transaction_type)
    }

    if (options?.startDate) {
      query = query.gte('transaction_date', options.startDate)
    }

    if (options?.endDate) {
      query = query.lte('transaction_date', options.endDate)
    }

    if (options?.limit) {
      query = query.limit(options.limit)
    }

    if (options?.offset) {
      query = query.range(options.offset, options.offset + (options.limit || 20) - 1)
    }

    const { data, error, count } = await query

    if (error) {
      throw new Error(`Failed to fetch investment transactions: ${error.message}`)
    }

    return { data: data as IInvestmentTransaction[], count: count || 0 }
  }

  /**
   * Get investment holdings for a user or specific account
   */
  static async getInvestmentHoldings(accountId?: string): Promise<IInvestmentHolding[]> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    let query = supabase
      .from('investment_holdings')
      .select(`
        *,
        account:accounts(*)
      `)
      .order('symbol')

    if (accountId) {
      query = query.eq('account_id', accountId)
    } else {
      // Filter by user's accounts
      const { data: userAccounts } = await supabase
        .from('accounts')
        .select('id')
        .eq('user_id', user.id)
        .eq('account_type', 'investment')

      if (userAccounts && userAccounts.length > 0) {
        const accountIds = userAccounts.map(acc => acc.id)
        query = query.in('account_id', accountIds)
      } else {
        return []
      }
    }

    const { data, error } = await query

    if (error) {
      throw new Error(`Failed to fetch investment holdings: ${error.message}`)
    }

    return data as IInvestmentHolding[]
  }

  /**
   * Get portfolio summary for all investment accounts
   */
  static async getPortfolioSummary(): Promise<{
    totalValue: number
    totalCost: number
    totalGainLoss: number
    totalGainLossPercent: number
    holdingsBySymbol: Record<string, {
      symbol: string
      totalQuantity: number
      averageCost: number
      currentPrice?: number
      marketValue?: number
      gainLoss?: number
      gainLossPercent?: number
    }>
  }> {
    const holdings = await this.getInvestmentHoldings()

    let totalValue = 0
    let totalCost = 0
    const holdingsBySymbol: Record<string, any> = {}

    holdings.forEach(holding => {
      const symbol = holding.symbol
      const quantity = holding.quantity
      const avgCost = holding.average_cost
      const currentPrice = holding.current_price || avgCost
      const marketValue = quantity * currentPrice
      const costBasis = quantity * avgCost
      const gainLoss = marketValue - costBasis
      const gainLossPercent = costBasis > 0 ? (gainLoss / costBasis) * 100 : 0

      if (!holdingsBySymbol[symbol]) {
        holdingsBySymbol[symbol] = {
          symbol,
          totalQuantity: 0,
          averageCost: 0,
          currentPrice,
          marketValue: 0,
          gainLoss: 0,
          gainLossPercent: 0,
        }
      }

      // Aggregate holdings for the same symbol across accounts
      const existing = holdingsBySymbol[symbol]
      const newTotalQuantity = existing.totalQuantity + quantity
      const newTotalCost = (existing.totalQuantity * existing.averageCost) + (quantity * avgCost)
      
      holdingsBySymbol[symbol] = {
        ...existing,
        totalQuantity: newTotalQuantity,
        averageCost: newTotalQuantity > 0 ? newTotalCost / newTotalQuantity : 0,
        marketValue: existing.marketValue + marketValue,
        gainLoss: existing.gainLoss + gainLoss,
      }

      // Recalculate percentage
      const totalCostBasis = holdingsBySymbol[symbol].totalQuantity * holdingsBySymbol[symbol].averageCost
      holdingsBySymbol[symbol].gainLossPercent = totalCostBasis > 0 
        ? (holdingsBySymbol[symbol].gainLoss / totalCostBasis) * 100 
        : 0

      totalValue += marketValue
      totalCost += costBasis
    })

    const totalGainLoss = totalValue - totalCost
    const totalGainLossPercent = totalCost > 0 ? (totalGainLoss / totalCost) * 100 : 0

    return {
      totalValue,
      totalCost,
      totalGainLoss,
      totalGainLossPercent,
      holdingsBySymbol,
    }
  }

  /**
   * Update current prices for holdings (would typically be called by a background job)
   */
  static async updateHoldingPrices(priceUpdates: { symbol: string; price: number }[]): Promise<void> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    for (const update of priceUpdates) {
      const marketValue = update.price // Will be calculated by trigger
      
      const { error } = await supabase
        .from('investment_holdings')
        .update({
          current_price: update.price,
          market_value: marketValue,
          last_updated: new Date().toISOString(),
        })
        .eq('symbol', update.symbol)

      if (error) {
        console.error(`Failed to update price for ${update.symbol}:`, error)
      }
    }
  }
}

import { supabase } from './supabase'
import type { ITransaction, ICategory, IAccount, TransactionType } from '../types'
import type { TablesInsert, TablesUpdate } from '../database.types'
import { TransferService } from './transfers'
import { InvestmentService } from './investments'

export class TransactionService {
  /**
   * Create a transaction of any type
   */
  static async createTransaction(data: {
    amount: number
    description?: string
    transaction_date: Date
    transaction_type: TransactionType
    fees?: number
    
    // Basic transaction fields
    category_id?: string
    account_id?: string
    
    // Transfer-specific fields
    to_account_id?: string
    
    // Investment-specific fields
    investment_symbol?: string
    investment_quantity?: number
    investment_price?: number
    funding_account_id?: string // For investment purchases
  }): Promise<ITransaction> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    // Handle different transaction types
    switch (data.transaction_type) {
      case 'transfer':
        if (!data.account_id || !data.to_account_id) {
          throw new Error('Source and destination accounts are required for transfers')
        }
        
        const transfer = await TransferService.createTransfer({
          amount: data.amount,
          description: data.description,
          from_account_id: data.account_id,
          to_account_id: data.to_account_id,
          transaction_date: data.transaction_date.toISOString().split('T')[0],
          fees: data.fees,
        })
        
        // Return the outgoing transaction as the primary transaction
        const { data: transferTransactions } = await supabase
          .from('transactions')
          .select(`
            *,
            category:categories(*),
            account:accounts(*),
            to_account:accounts!transactions_to_account_id_fkey(*)
          `)
          .eq('transfer_id', transfer.transfer_id)
          .eq('account_id', data.account_id)
          .single()
        
        return transferTransactions as ITransaction

      case 'investment_buy':
      case 'investment_sell':
        if (!data.account_id || !data.investment_symbol || !data.investment_quantity || !data.investment_price) {
          throw new Error('Investment account, symbol, quantity, and price are required for investment transactions')
        }
        
        const investment = await InvestmentService.createInvestmentTransaction({
          amount: data.amount,
          description: data.description,
          account_id: data.account_id,
          investment_symbol: data.investment_symbol,
          investment_quantity: data.investment_quantity,
          investment_price: data.investment_price,
          transaction_type: data.transaction_type,
          transaction_date: data.transaction_date.toISOString().split('T')[0],
          fees: data.fees,
        }, data.funding_account_id)
        
        return investment as ITransaction

      case 'income':
      case 'expense':
      case 'dividend':
        if (!data.category_id || !data.account_id) {
          throw new Error('Category and account are required for income/expense transactions')
        }
        
        const transactionData: TablesInsert<'transactions'> = {
          amount: data.amount,
          description: data.description || null,
          category_id: data.category_id,
          account_id: data.account_id,
          transaction_type: data.transaction_type,
          transaction_date: data.transaction_date.toISOString().split('T')[0],
          transaction_status: 'completed',
          fees: data.fees || 0,
          investment_symbol: data.investment_symbol || null,
          user_id: user.id,
        }

        const { data: transaction, error } = await supabase
          .from('transactions')
          .insert(transactionData)
          .select(`
            *,
            category:categories(*),
            account:accounts(*)
          `)
          .single()

        if (error) {
          throw new Error(`Failed to create transaction: ${error.message}`)
        }

        return transaction as ITransaction

      default:
        throw new Error(`Unsupported transaction type: ${data.transaction_type}`)
    }
  }

  /**
   * Get all transactions for the current user with enhanced filtering
   */
  static async getTransactions(options?: {
    limit?: number
    offset?: number
    categoryId?: string
    accountId?: string
    startDate?: string
    endDate?: string
    searchQuery?: string
    transactionType?: TransactionType | TransactionType[]
    transactionStatus?: string
    includeTransfers?: boolean
    includeInvestments?: boolean
  }): Promise<{ data: ITransaction[]; count: number }> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    let query = supabase
      .from('transactions')
      .select(`
        *,
        category:categories(*),
        account:accounts(*),
        to_account:accounts!transactions_to_account_id_fkey(*)
      `, { count: 'exact' })
      .eq('user_id', user.id)
      .order('transaction_date', { ascending: false })
      .order('created_at', { ascending: false })

    // Apply filters
    if (options?.categoryId) {
      query = query.eq('category_id', options.categoryId)
    }

    if (options?.accountId) {
      query = query.or(`account_id.eq.${options.accountId},to_account_id.eq.${options.accountId}`)
    }

    if (options?.startDate) {
      query = query.gte('transaction_date', options.startDate)
    }

    if (options?.endDate) {
      query = query.lte('transaction_date', options.endDate)
    }

    if (options?.transactionType) {
      if (Array.isArray(options.transactionType)) {
        query = query.in('transaction_type', options.transactionType)
      } else {
        query = query.eq('transaction_type', options.transactionType)
      }
    }

    if (options?.transactionStatus) {
      query = query.eq('transaction_status', options.transactionStatus)
    }

    // Filter out transfers and investments if not explicitly requested
    if (!options?.includeTransfers && !options?.includeInvestments) {
      query = query.in('transaction_type', ['income', 'expense', 'dividend'])
    } else if (!options?.includeTransfers) {
      query = query.neq('transaction_type', 'transfer')
    } else if (!options?.includeInvestments) {
      query = query.not('transaction_type', 'in', '(investment_buy,investment_sell)')
    }

    if (options?.searchQuery) {
      // Search in description, category name, and investment symbol
      query = query.or(
        `description.ilike.%${options.searchQuery}%,` +
        `investment_symbol.ilike.%${options.searchQuery}%`
      )
    }

    // Apply pagination
    if (options?.limit) {
      query = query.limit(options.limit)
    }

    if (options?.offset) {
      query = query.range(options.offset, (options.offset + (options.limit || 10)) - 1)
    }

    const { data, error, count } = await query

    if (error) {
      throw new Error(`Failed to fetch transactions: ${error.message}`)
    }

    return {
      data: data as ITransaction[],
      count: count || 0
    }
  }

  /**
   * Get a specific transaction by ID
   */
  static async getTransaction(id: string): Promise<ITransaction> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    const { data, error } = await supabase
      .from('transactions')
      .select(`
        *,
        category:categories(*),
        account:accounts(*),
        to_account:accounts!transactions_to_account_id_fkey(*)
      `)
      .eq('id', id)
      .eq('user_id', user.id)
      .single()

    if (error) {
      throw new Error(`Failed to fetch transaction: ${error.message}`)
    }

    return data as ITransaction
  }

  /**
   * Update a transaction (limited to basic transactions, not transfers or investments)
   */
  static async updateTransaction(id: string, updates: {
    amount?: number
    description?: string
    category_id?: string
    transaction_date?: Date
    fees?: number
  }): Promise<ITransaction> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    // First check if this is a basic transaction (not transfer or investment)
    const { data: existingTransaction } = await supabase
      .from('transactions')
      .select('transaction_type, transfer_id')
      .eq('id', id)
      .eq('user_id', user.id)
      .single()

    if (!existingTransaction) {
      throw new Error('Transaction not found')
    }

    if (existingTransaction.transaction_type === 'transfer' || 
        existingTransaction.transaction_type === 'investment_buy' || 
        existingTransaction.transaction_type === 'investment_sell') {
      throw new Error('Cannot update transfer or investment transactions through this method')
    }

    const updateData: TablesUpdate<'transactions'> = {
      updated_at: new Date().toISOString(),
    }

    if (updates.amount !== undefined) updateData.amount = updates.amount
    if (updates.description !== undefined) updateData.description = updates.description
    if (updates.category_id !== undefined) updateData.category_id = updates.category_id
    if (updates.fees !== undefined) updateData.fees = updates.fees
    if (updates.transaction_date !== undefined) {
      updateData.transaction_date = updates.transaction_date.toISOString().split('T')[0]
    }

    const { data: transaction, error } = await supabase
      .from('transactions')
      .update(updateData)
      .eq('id', id)
      .eq('user_id', user.id)
      .select(`
        *,
        category:categories(*),
        account:accounts(*)
      `)
      .single()

    if (error) {
      throw new Error(`Failed to update transaction: ${error.message}`)
    }

    return transaction as ITransaction
  }

  /**
   * Delete a transaction
   */
  static async deleteTransaction(id: string): Promise<void> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    // Check if this is a transfer transaction
    const { data: transaction } = await supabase
      .from('transactions')
      .select('transaction_type, transfer_id')
      .eq('id', id)
      .eq('user_id', user.id)
      .single()

    if (!transaction) {
      throw new Error('Transaction not found')
    }

    if (transaction.transaction_type === 'transfer' && transaction.transfer_id) {
      // Delete all transactions with the same transfer_id
      const { error } = await supabase
        .from('transactions')
        .delete()
        .eq('transfer_id', transaction.transfer_id)
        .eq('user_id', user.id)

      if (error) {
        throw new Error(`Failed to delete transfer: ${error.message}`)
      }
    } else {
      // Delete single transaction
      const { error } = await supabase
        .from('transactions')
        .delete()
        .eq('id', id)
        .eq('user_id', user.id)

      if (error) {
        throw new Error(`Failed to delete transaction: ${error.message}`)
      }
    }
  }

  /**
   * Get transaction summary for a date range
   */
  static async getTransactionSummary(options?: {
    startDate?: string
    endDate?: string
    accountId?: string
  }): Promise<{
    totalIncome: number
    totalExpenses: number
    totalTransfers: number
    totalInvestments: number
    netFlow: number
    transactionCount: number
  }> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    let query = supabase
      .from('transactions')
      .select('transaction_type, amount')
      .eq('user_id', user.id)
      .eq('transaction_status', 'completed')

    if (options?.startDate) {
      query = query.gte('transaction_date', options.startDate)
    }

    if (options?.endDate) {
      query = query.lte('transaction_date', options.endDate)
    }

    if (options?.accountId) {
      query = query.or(`account_id.eq.${options.accountId},to_account_id.eq.${options.accountId}`)
    }

    const { data, error } = await query

    if (error) {
      throw new Error(`Failed to fetch transaction summary: ${error.message}`)
    }

    const summary = {
      totalIncome: 0,
      totalExpenses: 0,
      totalTransfers: 0,
      totalInvestments: 0,
      netFlow: 0,
      transactionCount: data?.length || 0,
    }

    data?.forEach(transaction => {
      switch (transaction.transaction_type) {
        case 'income':
        case 'dividend':
          summary.totalIncome += transaction.amount
          break
        case 'expense':
          summary.totalExpenses += transaction.amount
          break
        case 'transfer':
          summary.totalTransfers += transaction.amount
          break
        case 'investment_buy':
        case 'investment_sell':
          summary.totalInvestments += transaction.amount
          break
      }
    })

    summary.netFlow = summary.totalIncome - summary.totalExpenses

    return summary
  }
}

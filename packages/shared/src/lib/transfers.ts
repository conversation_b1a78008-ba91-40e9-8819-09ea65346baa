import { supabase } from './supabase'
import type { ITransaction, ITransferForm, ITransferTransaction } from '../types'
import type { TablesInsert } from '../database.types'
import { v4 as uuidv4 } from 'uuid'

export class TransferService {
  /**
   * Create a transfer between two accounts
   * This creates two linked transactions: one debit and one credit
   */
  static async createTransfer(transferData: ITransferForm): Promise<ITransferTransaction> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    // Validate accounts exist and belong to user
    const { data: fromAccount } = await supabase
      .from('accounts')
      .select('*')
      .eq('id', transferData.from_account_id)
      .eq('user_id', user.id)
      .single()

    const { data: toAccount } = await supabase
      .from('accounts')
      .select('*')
      .eq('id', transferData.to_account_id)
      .eq('user_id', user.id)
      .single()

    if (!fromAccount || !toAccount) {
      throw new Error('Invalid account(s) specified')
    }

    if (fromAccount.id === toAccount.id) {
      throw new Error('Cannot transfer to the same account')
    }

    // Check if source account has sufficient balance (except for credit cards)
    if (fromAccount.account_type !== 'credit_card' && 
        fromAccount.current_balance < transferData.amount) {
      throw new Error('Insufficient balance in source account')
    }

    // Generate a unique transfer ID to link the transactions
    const transferId = uuidv4()

    // Get system transfer categories
    const { data: transferOutCategory } = await supabase
      .from('categories')
      .select('id')
      .eq('name', 'Transfer Out')
      .eq('is_system', true)
      .single()

    const { data: transferInCategory } = await supabase
      .from('categories')
      .select('id')
      .eq('name', 'Transfer In')
      .eq('is_system', true)
      .single()

    if (!transferOutCategory || !transferInCategory) {
      throw new Error('Transfer categories not found')
    }

    // Create the outgoing transaction (debit from source account)
    const outgoingTransaction: TablesInsert<'transactions'> = {
      amount: transferData.amount,
      description: transferData.description || `Transfer to ${toAccount.name}`,
      category_id: transferOutCategory.id,
      account_id: transferData.from_account_id,
      to_account_id: transferData.to_account_id,
      transfer_id: transferId,
      transaction_type: 'transfer',
      transaction_date: transferData.transaction_date,
      transaction_status: 'completed',
      fees: transferData.fees || 0,
      user_id: user.id,
    }

    // Create the incoming transaction (credit to destination account)
    const incomingTransaction: TablesInsert<'transactions'> = {
      amount: transferData.amount,
      description: transferData.description || `Transfer from ${fromAccount.name}`,
      category_id: transferInCategory.id,
      account_id: transferData.to_account_id,
      to_account_id: transferData.from_account_id, // Reference back to source
      transfer_id: transferId,
      transaction_type: 'transfer',
      transaction_date: transferData.transaction_date,
      transaction_status: 'completed',
      fees: 0, // Fees are only applied to the outgoing transaction
      user_id: user.id,
    }

    // Insert both transactions in a transaction
    const { data: outgoingData, error: outgoingError } = await supabase
      .from('transactions')
      .insert(outgoingTransaction)
      .select(`
        *,
        category:categories(*),
        account:accounts(*),
        to_account:accounts!transactions_to_account_id_fkey(*)
      `)
      .single()

    if (outgoingError) {
      throw new Error(`Failed to create outgoing transaction: ${outgoingError.message}`)
    }

    const { data: incomingData, error: incomingError } = await supabase
      .from('transactions')
      .insert(incomingTransaction)
      .select(`
        *,
        category:categories(*),
        account:accounts(*),
        to_account:accounts!transactions_to_account_id_fkey(*)
      `)
      .single()

    if (incomingError) {
      // If incoming transaction fails, we should rollback the outgoing transaction
      await supabase
        .from('transactions')
        .delete()
        .eq('id', outgoingData.id)
      
      throw new Error(`Failed to create incoming transaction: ${incomingError.message}`)
    }

    // Return a combined transfer object
    return {
      id: transferId,
      amount: transferData.amount,
      description: transferData.description,
      from_account_id: transferData.from_account_id,
      to_account_id: transferData.to_account_id,
      transfer_id: transferId,
      transaction_date: transferData.transaction_date,
      fees: transferData.fees,
      user_id: user.id,
      from_account: fromAccount,
      to_account: toAccount,
    }
  }

  /**
   * Get all transfers for the current user
   */
  static async getTransfers(options?: {
    account_id?: string
    startDate?: string
    endDate?: string
    limit?: number
    offset?: number
  }): Promise<{ data: ITransferTransaction[]; count: number }> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    // Get unique transfer IDs first
    let transferQuery = supabase
      .from('transactions')
      .select('transfer_id, transaction_date', { count: 'exact' })
      .eq('user_id', user.id)
      .eq('transaction_type', 'transfer')
      .not('transfer_id', 'is', null)
      .order('transaction_date', { ascending: false })

    if (options?.account_id) {
      transferQuery = transferQuery.or(`account_id.eq.${options.account_id},to_account_id.eq.${options.account_id}`)
    }

    if (options?.startDate) {
      transferQuery = transferQuery.gte('transaction_date', options.startDate)
    }

    if (options?.endDate) {
      transferQuery = transferQuery.lte('transaction_date', options.endDate)
    }

    if (options?.limit) {
      transferQuery = transferQuery.limit(options.limit)
    }

    if (options?.offset) {
      transferQuery = transferQuery.range(options.offset, options.offset + (options.limit || 20) - 1)
    }

    const { data: transferIds, error: transferError, count } = await transferQuery

    if (transferError) {
      throw new Error(`Failed to fetch transfers: ${transferError.message}`)
    }

    if (!transferIds || transferIds.length === 0) {
      return { data: [], count: count || 0 }
    }

    // Get the actual transfer transactions
    const uniqueTransferIds = [...new Set(transferIds.map(t => t.transfer_id))]
    
    const { data: transactions, error: transactionError } = await supabase
      .from('transactions')
      .select(`
        *,
        category:categories(*),
        account:accounts(*),
        to_account:accounts!transactions_to_account_id_fkey(*)
      `)
      .in('transfer_id', uniqueTransferIds)
      .eq('user_id', user.id)
      .eq('transaction_type', 'transfer')

    if (transactionError) {
      throw new Error(`Failed to fetch transfer transactions: ${transactionError.message}`)
    }

    // Group transactions by transfer_id and create transfer objects
    const transferMap = new Map<string, ITransaction[]>()
    
    transactions?.forEach(transaction => {
      if (transaction.transfer_id) {
        if (!transferMap.has(transaction.transfer_id)) {
          transferMap.set(transaction.transfer_id, [])
        }
        transferMap.get(transaction.transfer_id)!.push(transaction as ITransaction)
      }
    })

    const transfers: ITransferTransaction[] = []
    
    transferMap.forEach((transactionPair, transferId) => {
      if (transactionPair.length === 2) {
        // Find the outgoing transaction (the one with fees or the one from the source account)
        const outgoing = transactionPair.find(t => t.fees && t.fees > 0) || transactionPair[0]
        const incoming = transactionPair.find(t => t.id !== outgoing.id)!

        transfers.push({
          id: transferId,
          amount: outgoing.amount,
          description: outgoing.description,
          from_account_id: outgoing.account_id!,
          to_account_id: outgoing.to_account_id!,
          transfer_id: transferId,
          transaction_date: outgoing.transaction_date,
          fees: outgoing.fees,
          user_id: user.id,
          from_account: outgoing.account,
          to_account: outgoing.to_account,
        })
      }
    })

    // Sort by transaction date
    transfers.sort((a, b) => new Date(b.transaction_date).getTime() - new Date(a.transaction_date).getTime())

    return { data: transfers, count: count || 0 }
  }

  /**
   * Get a specific transfer by transfer ID
   */
  static async getTransfer(transferId: string): Promise<ITransferTransaction> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    const { data: transactions, error } = await supabase
      .from('transactions')
      .select(`
        *,
        category:categories(*),
        account:accounts(*),
        to_account:accounts!transactions_to_account_id_fkey(*)
      `)
      .eq('transfer_id', transferId)
      .eq('user_id', user.id)
      .eq('transaction_type', 'transfer')

    if (error) {
      throw new Error(`Failed to fetch transfer: ${error.message}`)
    }

    if (!transactions || transactions.length !== 2) {
      throw new Error('Transfer not found or incomplete')
    }

    // Find the outgoing transaction
    const outgoing = transactions.find(t => t.fees && t.fees > 0) || transactions[0]
    
    return {
      id: transferId,
      amount: outgoing.amount,
      description: outgoing.description,
      from_account_id: outgoing.account_id!,
      to_account_id: outgoing.to_account_id!,
      transfer_id: transferId,
      transaction_date: outgoing.transaction_date,
      fees: outgoing.fees,
      user_id: user.id,
      from_account: outgoing.account,
      to_account: outgoing.to_account,
    }
  }

  /**
   * Cancel a transfer (mark both transactions as cancelled)
   */
  static async cancelTransfer(transferId: string): Promise<void> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    const { error } = await supabase
      .from('transactions')
      .update({ 
        transaction_status: 'cancelled',
        updated_at: new Date().toISOString()
      })
      .eq('transfer_id', transferId)
      .eq('user_id', user.id)
      .eq('transaction_type', 'transfer')

    if (error) {
      throw new Error(`Failed to cancel transfer: ${error.message}`)
    }
  }
}

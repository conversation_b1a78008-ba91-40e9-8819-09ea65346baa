'use client'

import Link from 'next/link'
import AccountDropdown from './AccountDropdown'
import ThemeToggleButton from './ThemeToggleButton'

type NavbarProps = {
  currentPage: 'dashboard' | 'expenses' | 'budgets' | 'profile'
}

export default function Navbar({ currentPage }: NavbarProps) {
  const navItems = [
    {
      href: '/dashboard',
      label: 'Dashboard',
      key: 'dashboard' as const,
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 5a2 2 0 012-2h2a2 2 0 012 2v2H8V5z" />
        </svg>
      )
    },
    {
      href: '/expenses',
      label: 'Expenses',
      key: 'expenses' as const,
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 14l6-6m-5.5.5h.01m4.99 5h.01M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16l3.5-2 3.5 2 3.5-2 3.5 2z" />
        </svg>
      )
    },
    {
      href: '/budgets',
      label: 'Budgets',
      key: 'budgets' as const,
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 002 2z" />
        </svg>
      )
    }
  ]

  return (
    <nav className="bg-surface-elevated border-b border-border-light shadow-sm">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          {/* Logo */}
          <div className="flex items-center">
            <Link href="/dashboard" className="flex items-center gap-3 text-xl font-semibold text-text-primary hover:text-text-secondary transition-colors">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 114 0 2 2 0 01-4 0zm8-2a2 2 0 100 4 2 2 0 000-4z" clipRule="evenodd" />
                </svg>
              </div>
              Portfolio Tracker
            </Link>
          </div>

          {/* Navigation Items */}
          <div className="hidden md:flex items-center space-x-1">
            {navItems.map((item) => {
              const isActive = item.key === currentPage
              
              if (isActive) {
                return (
                  <div
                    key={item.key}
                    className="bg-gradient-to-r from-blue-500 to-purple-600 px-4 py-2 rounded-lg text-white text-sm font-medium flex items-center gap-2"
                  >
                    {item.icon}
                    <span>{item.label}</span>
                  </div>
                )
              }
              
              return (
                <Link
                  key={item.key}
                  href={item.href}
                  className="flex items-center gap-2 px-4 py-2 rounded-lg text-text-secondary hover:text-text-primary hover:bg-surface transition-all"
                >
                  {item.icon}
                  <span>{item.label}</span>
                </Link>
              )
            })}
          </div>

          {/* Theme Toggle & Account Dropdown */}
          <div className="flex items-center gap-3">
            <ThemeToggleButton />
            <AccountDropdown />
          </div>
        </div>
      </div>
    </nav>
  )
}
'use client'

import { useState, useEffect } from 'react'
import {
  TransactionService,
  CategoryService,
  type ITransaction,
  type ICategory,
  type TransactionFormData
} from '@repo/shared'
import { TransactionForm } from '../../components/TransactionForm'
import TransactionListWeb from '../../components/TransactionList'
import { TransactionTemplates } from '../../components/TransactionTemplates'
import { Modal } from '@/components/Modal'
import { toast } from 'react-hot-toast'
import ProtectedRoute from '@/components/ProtectedRoute'
import Navbar from '../../components/Navbar'

export default function ExpensesPage() {
  const [categories, setCategories] = useState<ICategory[]>([])
  const [submitting, setSubmitting] = useState(false)
  const [showForm, setShowForm] = useState(false)
  const [showTemplates, setShowTemplates] = useState(false)
  const [editingTransaction, setEditingTransaction] = useState<ITransaction | null>(null)
  const [templateData, setTemplateData] = useState<any>(null)
  const [refreshKey, setRefreshKey] = useState(0)

  useEffect(() => {
    loadCategories()
  }, [])

  const loadCategories = async () => {
    try {
      const categoriesData = await CategoryService.getCategories({ is_active: true })
      setCategories(categoriesData)
    } catch (error) {
      console.error('Failed to load categories:', error)
      toast.error('Failed to load categories')
    }
  }

  const handleSubmit = async (data: TransactionFormData) => {
    setSubmitting(true)
    try {
      if (editingTransaction) {
        // For basic transactions, use the update method
        if (editingTransaction.transaction_type === 'income' || editingTransaction.transaction_type === 'expense') {
          await TransactionService.updateTransaction(editingTransaction.id, {
            amount: data.amount,
            description: data.description,
            category_id: data.category_id,
            transaction_date: data.transaction_date,
            fees: data.fees,
          })
        } else {
          throw new Error('Cannot update transfer or investment transactions')
        }
        toast.success('Transaction updated successfully!')
      } else {
        await TransactionService.createTransaction(data)
        const transactionTypeLabel = {
          income: 'Income',
          expense: 'Expense',
          transfer: 'Transfer',
          investment_buy: 'Investment Purchase',
          investment_sell: 'Investment Sale',
          dividend: 'Dividend'
        }[data.transaction_type] || 'Transaction'

        toast.success(`${transactionTypeLabel} added successfully!`)
      }
      setShowForm(false)
      setEditingTransaction(null)
      setTemplateData(null)
      setRefreshKey(prev => prev + 1)
    } catch (error) {
      console.error('Failed to save transaction:', error)
      toast.error(`Failed to ${editingTransaction ? 'update' : 'add'} transaction`)
    } finally {
      setSubmitting(false)
    }
  }

  const handleEditTransaction = (transaction: ITransaction) => {
    setEditingTransaction(transaction)
    setShowForm(true)
  }

  const handleCancelEdit = () => {
    setEditingTransaction(null)
    setTemplateData(null)
    setShowForm(false)
  }

  const handleUseTemplate = (template: Omit<any, 'id' | 'user_id' | 'created_at' | 'updated_at'>) => {
    setTemplateData({
      amount: template.amount?.toString() || '',
      category_id: template.category_id || '',
      description: template.description || '',
      transaction_date: new Date(),
      transaction_type: template.transaction_type || 'expense',
      account_id: '',
      to_account_id: '',
      fees: '',
      investment_symbol: '',
      investment_quantity: '',
      investment_price: '',
      funding_account_id: '',
    })
    setShowTemplates(false)
    setShowForm(true)
  }

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-background">
        <Navbar currentPage="expenses" />

        <main className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="space-y-8">
            {/* Page Header */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-6">
              <div>
                <h1 className="text-4xl font-bold text-text-primary">Transactions</h1>
                <p className="text-text-secondary text-lg mt-2">Track your income, expenses, transfers, and investments</p>
              </div>
              <div className="flex items-center gap-3">
                {editingTransaction && (
                  <button
                    onClick={handleCancelEdit}
                    className="bg-surface text-text-secondary border border-border px-6 py-3 rounded-lg font-medium hover:bg-surface-elevated transition-colors flex items-center gap-2"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                    Cancel Edit
                  </button>
                )}
                <button
                  onClick={() => setShowTemplates(true)}
                  className="bg-surface text-text-primary border border-border px-6 py-3 rounded-lg font-medium hover:bg-surface-elevated transition-colors flex items-center gap-2"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  Templates
                </button>
                <button
                  onClick={() => setShowForm(true)}
                  className="bg-gradient-to-r from-blue-500 to-purple-600 text-white px-6 py-3 rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105 flex items-center gap-2 font-semibold"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                  </svg>
                  Add Transaction
                </button>
              </div>
            </div>

            {/* Add/Edit Transaction Modal */}
            <Modal
              isOpen={showForm}
              onClose={() => {
                setShowForm(false)
                setEditingTransaction(null)
                setTemplateData(null)
              }}
              title={editingTransaction ? 'Edit Transaction' : templateData ? 'New Transaction from Template' : 'Add New Transaction'}
              size="xl"
            >
              <TransactionForm
                onSubmit={handleSubmit}
                loading={submitting}
                compact={true}
                initialData={editingTransaction ? {
                  amount: editingTransaction.amount?.toString() || '',
                  category_id: editingTransaction.category_id || '',
                  account_id: editingTransaction.account_id || '',
                  to_account_id: editingTransaction.to_account_id || '',
                  description: editingTransaction.description || '',
                  transaction_date: new Date(editingTransaction.transaction_date),
                  transaction_type: editingTransaction.transaction_type,
                  fees: editingTransaction.fees?.toString() || '',
                  investment_symbol: editingTransaction.investment_symbol || '',
                  investment_quantity: editingTransaction.investment_quantity?.toString() || '',
                  investment_price: editingTransaction.investment_price?.toString() || '',
                  funding_account_id: '',
                } : templateData ? templateData : undefined}
              />
            </Modal>

            {/* Templates Modal */}
            <Modal
              isOpen={showTemplates}
              onClose={() => setShowTemplates(false)}
              title="Transaction Templates"
              size="lg"
            >
              <div className="space-y-4">
                <p className="text-text-secondary">
                  Choose from your saved templates to quickly create new transactions.
                </p>
                <TransactionTemplates 
                  categories={categories}
                  onUseTemplate={handleUseTemplate}
                />
              </div>
            </Modal>

            {/* Enhanced Transactions List */}
            <TransactionListWeb
              key={refreshKey}
              onEditTransaction={handleEditTransaction}
            />
          </div>
        </main>

      </div>
    </ProtectedRoute>
  )
}